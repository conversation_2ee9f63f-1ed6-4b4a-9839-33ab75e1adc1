/**app.wxss**/
@import './styles/theme.wxss';
@import './styles/compatibility.wxss';

page {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  line-height: 1.6;
}

.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background: var(--gradient-soft);
}

/* 全局重置样式 */
view, text, button, input, textarea, image, scroll-view {
  box-sizing: border-box;
}

/* 通用按钮样式重置 */
button {
  border: none;
  outline: none;
  background: none;
  padding: 0;
  margin: 0;
  font-family: inherit;
}

button::after {
  border: none;
}

/* 输入框样式重置 */
input, textarea {
  font-family: inherit;
  outline: none;
}

/* 图片样式 */
image {
  display: block;
  max-width: 100%;
}

/* 滚动条样式在小程序中不支持，已移除 */