/* 兼容性样式文件 - 解决不同设备和版本的兼容性问题 */

/* ==================== 微信小程序特定修复 ==================== */

/* 修复小程序中的样式重置问题 */
page {
  background-color: #F8F9FA;
  color: #3C4043;
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

/* 修复小程序中的盒模型问题 */
view, text, button, input, textarea, image, scroll-view {
  box-sizing: border-box;
}

/* 修复小程序中的按钮样式问题 */
button {
  border: none;
  outline: none;
  background: none;
  padding: 0;
  margin: 0;
  font-family: inherit;
}

button::after {
  border: none;
}

/* ==================== 低版本兼容性修复 ==================== */

/* 为不支持CSS变量的环境提供固定值 */
.fallback-primary-color { color: #8BB6E8; }
.fallback-secondary-color { color: #5F6368; }
.fallback-bg-primary { background-color: #FFFFFF; }
.fallback-bg-secondary { background-color: #F8F9FA; }

/* 为不支持backdrop-filter的环境提供替代方案 */
.fallback-glass-card {
  background: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(0, 0, 0, 0.08);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  padding: 24rpx;
}

.fallback-glass-button {
  background: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(0, 0, 0, 0.08);
  border-radius: 16rpx;
  padding: 16rpx 32rpx;
  color: #3C4043;
  font-size: 28rpx;
  font-weight: 500;
}

/* ==================== 性能优化 ==================== */

/* 简化动画以提高性能 */
.simple-transition {
  transition: opacity 0.3s ease;
}

.simple-transform {
  transition: transform 0.3s ease;
}

/* 禁用复杂动画的类 */
.no-animation * {
  animation: none !important;
  transition: none !important;
}

/* ==================== 布局修复 ==================== */

/* Flexbox替代Grid的通用样式 */
.flex-grid {
  display: flex;
  flex-wrap: wrap;
}

.flex-grid-2 {
  display: flex;
  flex-direction: row;
}

.flex-grid-2 > * {
  flex: 1;
  margin-right: 16rpx;
}

.flex-grid-2 > *:last-child {
  margin-right: 0;
}

/* ==================== 字体修复 ==================== */

/* 确保字体在所有设备上正确显示 */
.safe-font {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

/* ==================== 图片修复 ==================== */

/* 修复图片显示问题 */
.safe-image {
  display: block;
  max-width: 100%;
  height: auto;
}

/* ==================== 输入框修复 ==================== */

/* 修复输入框样式问题 */
.safe-input {
  font-family: inherit;
  outline: none;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
  background: #FFFFFF;
  color: #3C4043;
}

/* ==================== 滚动修复 ==================== */

/* 修复滚动区域样式 */
.safe-scroll {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

/* ==================== 安全区域适配 ==================== */

/* 底部安全区域适配 */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
  padding-bottom: constant(safe-area-inset-bottom); /* iOS < 11.2 */
}

/* 顶部安全区域适配 */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
  padding-top: constant(safe-area-inset-top); /* iOS < 11.2 */
}

/* ==================== 调试样式 ==================== */

/* 调试时使用的边框样式 */
.debug-border {
  border: 1rpx solid red;
}

.debug-bg {
  background: rgba(255, 0, 0, 0.1);
}
